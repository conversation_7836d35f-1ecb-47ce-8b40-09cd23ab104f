<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SecureNews - Premium Content</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .paywall-container {
            min-height: 80vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .paywall-card {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            text-align: center;
        }
        
        .security-badge {
            background: #dc3545;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 1rem;
        }
        
        .security-maximum { background: #6f42c1; }
        .security-high { background: #dc3545; }
        
        .feature-list {
            text-align: left;
            margin: 2rem 0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .feature-icon {
            width: 24px;
            height: 24px;
            background: #28a745;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
            font-weight: bold;
        }
        
        .article-preview {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            margin: 2rem 0;
            position: relative;
            overflow: hidden;
        }
        
        .article-preview::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: linear-gradient(transparent, #f8f9fa);
        }
        
        .btn-premium {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            color: white;
            font-weight: bold;
            transition: transform 0.2s;
        }
        
        .btn-premium:hover {
            transform: translateY(-2px);
            color: white;
        }
        
        .security-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 2rem;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">🔒 SecureNews Lab</a>
            <div class="navbar-nav ms-auto">
                <button class="btn btn-outline-light btn-sm" data-bs-toggle="modal" data-bs-target="#loginModal">Login</button>
            </div>
        </div>
    </nav>

    <div class="paywall-container">
        <div class="paywall-card">
            <div class="security-badge security-<%= securityLevel || 'high' %>">
                🔒 <%= (securityLevel || 'high').toUpperCase() %> SECURITY
            </div>
            
            <h2 class="mb-4">Premium Content Access Required</h2>
            
            <div class="article-preview">
                <h4><%= article.title %></h4>
                <p><%= article.content %></p>
                <div class="text-muted">
                    <small>This is just a preview. Full article contains <%= Math.floor(Math.random() * 500) + 1000 %> more words.</small>
                </div>
            </div>
            
            <div class="feature-list">
                <div class="feature-item">
                    <div class="feature-icon">✓</div>
                    <div>Unlimited access to all premium articles</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">✓</div>
                    <div>Advanced cybersecurity analysis and insights</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">✓</div>
                    <div>Exclusive security research and case studies</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">✓</div>
                    <div>Early access to security tools and techniques</div>
                </div>
            </div>
            
            <button class="btn btn-premium btn-lg" data-bs-toggle="modal" data-bs-target="#loginModal">
                🚀 Get Premium Access
            </button>
            
            <div class="security-info">
                <strong>🛡️ Security Notice:</strong> This paywall implements 
                <% if (securityLevel === 'maximum') { %>
                    maximum security with JWT tokens, server-side validation, rate limiting, and encrypted sessions.
                <% } else if (securityLevel === 'high') { %>
                    high security with server-side validation, secure cookies, and authentication checks.
                <% } else { %>
                    standard security measures including session management and access controls.
                <% } %>
                <br><small class="text-muted">
                    Bypass attempts: <%= reason === 'no_subscription' ? 'Authentication required' : 
                                        reason === 'token_expired' ? 'Token validation failed' : 
                                        'Access denied' %>
                </small>
            </div>
        </div>
    </div>

    <!-- Login Modal -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">🔐 Secure Authentication</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <strong>Educational Lab:</strong> Use the provided test credentials to explore the security system.
                    </div>
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" required>
                            <div class="form-text">
                                Premium: <EMAIL> | Free: <EMAIL>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" required>
                            <div class="form-text">
                                Premium: premium123 | Free: password123
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            🔓 Authenticate
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Enhanced login functionality
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email, password })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    console.log('Authentication successful');
                    location.reload();
                } else {
                    alert('Authentication failed: ' + result.message);
                }
            } catch (error) {
                console.error('Login error:', error);
                alert('Login failed: Network error');
            }
        });
        
        // Security testing interface
        window.securityTest = {
            analyzePaywall: () => {
                console.log('Paywall Analysis:');
                console.log('- Security Level: <%= securityLevel || "high" %>');
                console.log('- Access Reason: <%= reason || "unknown" %>');
                console.log('- Article ID: <%= article.id %>');
                console.log('- Server-side validation: Active');
                console.log('- JWT requirement: <%= article.requiresValidToken ? "Yes" : "No" %>');
            },
            
            testBypassMethods: () => {
                console.log('Available bypass testing methods:');
                console.log('1. Cookie manipulation (limited effectiveness)');
                console.log('2. Header injection (server validates)');
                console.log('3. JWT token analysis (if available)');
                console.log('4. Session replay attacks');
                console.log('5. Rate limit testing');
                console.log('Use the Python tools for comprehensive testing.');
            }
        };
        
        // Auto-run analysis
        setTimeout(() => {
            console.log('🔒 Secure Paywall Loaded');
            window.securityTest.analyzePaywall();
        }, 1000);
    </script>
</body>
</html>
