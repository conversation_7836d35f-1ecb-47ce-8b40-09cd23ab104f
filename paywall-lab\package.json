{"name": "paywall-security-lab", "version": "1.0.0", "description": "Educational lab for teaching paywall bypass techniques", "main": "server.js", "scripts": {"start": "node secure_server.js", "dev": "nodemon secure_server.js", "start-basic": "node server.js", "start-secure": "node secure_server.js"}, "keywords": ["education", "security", "paywall", "cybersecurity"], "author": "Security Education Lab", "license": "MIT", "dependencies": {"express": "^4.18.2", "ejs": "^3.1.9", "cookie-parser": "^1.4.6", "express-session": "^1.17.3", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}}