#!/usr/bin/env python3
"""
Educational Browser Injector
Demonstrates browser automation-based paywall bypass techniques for cybersecurity education.

This tool uses Selenium to automate browser interactions and demonstrate various bypass methods.
For educational purposes only in controlled lab environments.
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import time
import argparse
import sys

class BrowserInjector:
    def __init__(self, base_url="http://localhost:3000", headless=False):
        self.base_url = base_url
        self.driver = None
        self.headless = headless
        
    def setup_browser(self):
        """Setup Chrome browser with appropriate options"""
        print("🌐 Setting up browser...")
        
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        chrome_options.add_argument("--no-sandbox")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ Browser setup complete")
            return True
        except Exception as e:
            print(f"❌ Browser setup failed: {e}")
            print("💡 Make sure ChromeDriver is installed and in PATH")
            return False
    
    def inject_cookies_via_browser(self):
        """Inject cookies using browser automation"""
        print("🍪 Injecting cookies via browser...")
        
        # Navigate to the site first
        self.driver.get(self.base_url)
        
        # Inject premium cookie
        self.driver.add_cookie({
            'name': 'premium_user',
            'value': 'true',
            'domain': 'localhost'
        })
        
        print("✅ Premium cookie injected via browser")
        return True
    
    def inject_javascript_bypass(self):
        """Inject JavaScript to bypass client-side restrictions"""
        print("💻 Injecting JavaScript bypass code...")
        
        # JavaScript to manipulate DOM and bypass client-side restrictions
        bypass_script = """
        // Remove paywall overlays
        var overlays = document.querySelectorAll('.paywall-overlay, .paywall-content');
        overlays.forEach(function(overlay) {
            overlay.style.display = 'none';
            overlay.remove();
        });
        
        // Set premium cookie
        document.cookie = 'premium_user=true; path=/';
        
        // Expose bypass functions
        window.educationalBypass = {
            removePremiumBadges: function() {
                var badges = document.querySelectorAll('.premium-badge');
                badges.forEach(function(badge) {
                    badge.style.display = 'none';
                });
            },
            
            showHiddenContent: function() {
                var hiddenElements = document.querySelectorAll('[style*="display: none"]');
                hiddenElements.forEach(function(element) {
                    element.style.display = 'block';
                });
            },
            
            bypassClientSideChecks: function() {
                // Override any client-side premium checks
                window.isPremiumUser = true;
                window.hasSubscription = true;
            }
        };
        
        // Execute bypass functions
        window.educationalBypass.removePremiumBadges();
        window.educationalBypass.showHiddenContent();
        window.educationalBypass.bypassClientSideChecks();
        
        console.log('Educational bypass injected successfully');
        return 'Bypass injected';
        """
        
        try:
            result = self.driver.execute_script(bypass_script)
            print("✅ JavaScript bypass injected")
            return True
        except Exception as e:
            print(f"❌ JavaScript injection failed: {e}")
            return False
    
    def manipulate_dom_elements(self):
        """Manipulate DOM elements to bypass restrictions"""
        print("🔧 Manipulating DOM elements...")
        
        try:
            # Remove paywall overlays
            overlays = self.driver.find_elements(By.CLASS_NAME, "paywall-overlay")
            for overlay in overlays:
                self.driver.execute_script("arguments[0].remove();", overlay)
            
            # Remove premium badges
            badges = self.driver.find_elements(By.CLASS_NAME, "premium-badge")
            for badge in badges:
                self.driver.execute_script("arguments[0].style.display = 'none';", badge)
            
            print("✅ DOM manipulation complete")
            return True
        except Exception as e:
            print(f"❌ DOM manipulation failed: {e}")
            return False
    
    def test_article_access(self, article_id=1):
        """Test access to premium article after bypass attempts"""
        print(f"\n🧪 Testing article {article_id} access...")
        
        article_url = f"{self.base_url}/article/{article_id}"
        self.driver.get(article_url)
        
        # Wait for page to load
        time.sleep(2)
        
        try:
            # Check if paywall is present
            paywall_elements = self.driver.find_elements(By.CLASS_NAME, "paywall-overlay")
            premium_content_alerts = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Premium Content')]")
            
            if paywall_elements or premium_content_alerts:
                print("❌ Paywall still active")
                return False
            else:
                # Check if we can see the full article content
                article_content = self.driver.find_elements(By.CLASS_NAME, "article-content")
                if article_content and len(article_content[0].text) > 100:
                    print("✅ Full article content accessible")
                    return True
                else:
                    print("⚠️  Partial access - content may be limited")
                    return False
                    
        except Exception as e:
            print(f"❌ Access test failed: {e}")
            return False
    
    def demonstrate_browser_bypass_techniques(self):
        """Demonstrate various browser-based bypass techniques"""
        print("🎓 Educational Browser Injection Demonstration")
        print("=" * 50)
        
        if not self.setup_browser():
            return False
        
        results = {}
        
        try:
            # Test 1: Cookie injection via browser
            print("\n1. Cookie Injection via Browser")
            self.inject_cookies_via_browser()
            results['cookie_injection'] = self.test_article_access(1)
            
            # Test 2: JavaScript bypass injection
            print("\n2. JavaScript Bypass Injection")
            self.driver.get(f"{self.base_url}/article/1")
            time.sleep(1)
            self.inject_javascript_bypass()
            results['javascript_bypass'] = self.test_article_access(1)
            
            # Test 3: DOM manipulation
            print("\n3. DOM Element Manipulation")
            self.driver.get(f"{self.base_url}/article/1")
            time.sleep(1)
            self.manipulate_dom_elements()
            results['dom_manipulation'] = self.test_article_access(1)
            
            # Test 4: URL parameter bypass
            print("\n4. URL Parameter Bypass")
            bypass_url = f"{self.base_url}/article/1?bypass=client"
            self.driver.get(bypass_url)
            time.sleep(2)
            results['url_parameter'] = self.test_article_access(1)
            
            # Summary
            print("\n📊 Browser Bypass Results Summary:")
            for test_name, success in results.items():
                status = '✅ Success' if success else '❌ Failed'
                print(f"   {test_name.replace('_', ' ').title()}: {status}")
            
            return any(results.values())
            
        finally:
            self.cleanup()
    
    def take_screenshot(self, filename="bypass_result.png"):
        """Take a screenshot of the current page"""
        try:
            self.driver.save_screenshot(filename)
            print(f"📸 Screenshot saved: {filename}")
            return True
        except Exception as e:
            print(f"❌ Screenshot failed: {e}")
            return False
    
    def cleanup(self):
        """Clean up browser resources"""
        if self.driver:
            print("🧹 Cleaning up browser...")
            self.driver.quit()
            print("✅ Browser cleanup complete")

def main():
    parser = argparse.ArgumentParser(description="Educational Browser Injector for Paywall Bypass Learning")
    parser.add_argument("--url", default="http://localhost:3000", help="Target URL (default: http://localhost:3000)")
    parser.add_argument("--article", type=int, default=1, help="Article ID to test (default: 1)")
    parser.add_argument("--headless", action="store_true", help="Run browser in headless mode")
    parser.add_argument("--demo", action="store_true", help="Run full demonstration")
    parser.add_argument("--screenshot", action="store_true", help="Take screenshot after bypass")
    
    args = parser.parse_args()
    
    print("🎓 Educational Browser Injector")
    print("⚠️  For educational use only in controlled environments!")
    print(f"🎯 Target: {args.url}")
    print()
    
    injector = BrowserInjector(args.url, args.headless)
    
    if args.demo:
        injector.demonstrate_browser_bypass_techniques()
    else:
        # Single test
        if injector.setup_browser():
            try:
                injector.inject_cookies_via_browser()
                injector.inject_javascript_bypass()
                success = injector.test_article_access(args.article)
                
                if args.screenshot:
                    injector.take_screenshot()
                    
            finally:
                injector.cleanup()

if __name__ == "__main__":
    main()
