# Educational Paywall Bypass Injectors - PowerShell Version
# For Cybersecurity Education Only

param(
    [string]$Injector = "",
    [switch]$Demo = $false,
    [string]$Url = "http://localhost:3000",
    [int]$Article = 1,
    [switch]$InstallDeps = $false
)

function Show-Banner {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Educational Paywall Bypass Injectors" -ForegroundColor Yellow
    Write-Host "For Cybersecurity Education Only" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
}

function Show-Menu {
    Write-Host "Select an injector to run:" -ForegroundColor Green
    Write-Host "1. <PERSON><PERSON> Injector (Basic)" -ForegroundColor White
    Write-Host "2. <PERSON>ie Injector (Full Demo)" -ForegroundColor White
    Write-Host "3. Header Injector (Basic)" -ForegroundColor White
    Write-Host "4. Header Injector (Full Demo)" -ForegroundColor White
    Write-Host "5. Browser Injector (Basic)" -ForegroundColor White
    Write-Host "6. Browser Injector (Full Demo)" -ForegroundColor White
    Write-Host "7. Run All Injectors (Full Demo)" -ForegroundColor White
    Write-Host "8. Install Dependencies" -ForegroundColor Yellow
    Write-Host "9. Exit" -ForegroundColor Red
    Write-Host ""
}

function Install-Dependencies {
    Write-Host "Installing Python dependencies..." -ForegroundColor Yellow
    
    # Check if pip is available
    try {
        pip --version | Out-Null
        pip install -r requirements.txt
        Write-Host "Dependencies installed successfully!" -ForegroundColor Green
    }
    catch {
        Write-Host "Error: pip not found. Please install Python and pip first." -ForegroundColor Red
        Write-Host "Download Python from: https://www.python.org/downloads/" -ForegroundColor Yellow
    }
}

function Run-CookieInjector {
    param([switch]$Demo)
    
    Write-Host "Running Cookie Injector..." -ForegroundColor Yellow
    
    if ($Demo) {
        python cookie_injector.py --demo --url $Url
    } else {
        python cookie_injector.py --url $Url --article $Article
    }
}

function Run-HeaderInjector {
    param([switch]$Demo)
    
    Write-Host "Running Header Injector..." -ForegroundColor Yellow
    
    if ($Demo) {
        python header_injector.py --demo --url $Url
    } else {
        python header_injector.py --url $Url --article $Article
    }
}

function Run-BrowserInjector {
    param([switch]$Demo)
    
    Write-Host "Running Browser Injector..." -ForegroundColor Yellow
    Write-Host "Note: This requires ChromeDriver to be installed" -ForegroundColor Cyan
    
    if ($Demo) {
        python browser_injector.py --demo --url $Url
    } else {
        python browser_injector.py --url $Url --article $Article
    }
}

function Run-AllInjectors {
    Write-Host "Running All Injectors (Full Demo)..." -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "=== Cookie Injector Demo ===" -ForegroundColor Magenta
    Run-CookieInjector -Demo
    Write-Host ""
    
    Write-Host "=== Header Injector Demo ===" -ForegroundColor Magenta
    Run-HeaderInjector -Demo
    Write-Host ""
    
    Write-Host "=== Browser Injector Demo ===" -ForegroundColor Magenta
    Run-BrowserInjector -Demo
}

# Main execution
Show-Banner

# Handle command line parameters
if ($InstallDeps) {
    Install-Dependencies
    exit
}

if ($Injector -ne "") {
    switch ($Injector.ToLower()) {
        "cookie" { Run-CookieInjector -Demo:$Demo }
        "header" { Run-HeaderInjector -Demo:$Demo }
        "browser" { Run-BrowserInjector -Demo:$Demo }
        "all" { Run-AllInjectors }
        default { 
            Write-Host "Unknown injector: $Injector" -ForegroundColor Red
            Write-Host "Available options: cookie, header, browser, all" -ForegroundColor Yellow
        }
    }
    exit
}

# Interactive menu
do {
    Show-Menu
    $choice = Read-Host "Enter your choice (1-9)"
    
    switch ($choice) {
        "1" { Run-CookieInjector }
        "2" { Run-CookieInjector -Demo }
        "3" { Run-HeaderInjector }
        "4" { Run-HeaderInjector -Demo }
        "5" { Run-BrowserInjector }
        "6" { Run-BrowserInjector -Demo }
        "7" { Run-AllInjectors }
        "8" { Install-Dependencies }
        "9" { 
            Write-Host "Goodbye!" -ForegroundColor Green
            exit 
        }
        default { 
            Write-Host "Invalid choice. Please select 1-9." -ForegroundColor Red 
        }
    }
    
    if ($choice -ne "9") {
        Write-Host ""
        Read-Host "Press Enter to continue..."
        Clear-Host
        Show-Banner
    }
} while ($choice -ne "9")
