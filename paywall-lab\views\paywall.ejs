<% layout('layout') -%>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <h1><%= article.title %></h1>
                    <span class="premium-badge">PREMIUM</span>
                </div>
                
                <div class="article-preview">
                    <p><%= article.content %></p>
                </div>
                
                <div class="alert alert-warning mt-3">
                    <h5>🔒 Premium Content</h5>
                    <p>This article requires a premium subscription to read in full.</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>Paywall Active</h5>
            </div>
            <div class="card-body">
                <p>This is an educational paywall with multiple protection layers:</p>
                
                <h6>Protection Methods:</h6>
                <ul class="small">
                    <li>✅ Client-side validation</li>
                    <li>✅ Cookie verification</li>
                    <li>✅ Session checking</li>
                    <li>✅ Header validation</li>
                    <li>✅ Referrer checking</li>
                </ul>
                
                <hr>
                
                <h6>Educational Bypass Techniques:</h6>
                <div class="small">
                    <p><strong>Method 1 - URL Parameter:</strong><br>
                    Add <code>?bypass=client</code> to the URL</p>
                    
                    <p><strong>Method 2 - Cookie Injection:</strong><br>
                    <button class="btn btn-sm btn-outline-primary" onclick="bypassWithCookie()">
                        Set Premium Cookie
                    </button></p>
                    
                    <p><strong>Method 3 - Direct API:</strong><br>
                    <button class="btn btn-sm btn-outline-primary" onclick="bypassWithAPI()">
                        Access via API
                    </button></p>
                    
                    <p><strong>Method 4 - Header Injection:</strong><br>
                    Use browser dev tools or proxy to add:<br>
                    <code>X-Premium-Access: granted</code></p>
                    
                    <p><strong>Method 5 - Referrer Spoofing:</strong><br>
                    Set referrer to contain "premium"</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5>Learning Objectives</h5>
            </div>
            <div class="card-body small">
                <ul>
                    <li>Understand paywall implementation weaknesses</li>
                    <li>Practice various bypass techniques</li>
                    <li>Learn about client vs server-side security</li>
                    <li>Explore HTTP header manipulation</li>
                    <li>Understand cookie-based vulnerabilities</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Client-side paywall overlay (easily bypassable) -->
<div id="paywallOverlay" class="paywall-overlay" style="display: none;">
    <div class="paywall-content">
        <h3>🔒 Premium Content</h3>
        <p>Subscribe to access this article</p>
        <button class="btn btn-primary me-2">Subscribe Now</button>
        <button class="btn btn-secondary" onclick="closePaywall()">Close</button>
        <hr>
        <small class="text-muted">
            Educational Note: This overlay can be removed with browser dev tools
        </small>
    </div>
</div>

<script>
    // Educational: Demonstrate client-side paywall weakness
    function showClientSidePaywall() {
        document.getElementById('paywallOverlay').style.display = 'flex';
    }
    
    function closePaywall() {
        document.getElementById('paywallOverlay').style.display = 'none';
    }
    
    // Bypass demonstrations
    function bypassWithCookie() {
        bypassPaywall.setCookie('premium_user', 'true');
        alert('Premium cookie set! Refresh the page to see the effect.');
    }
    
    async function bypassWithAPI() {
        const article = await bypassPaywall.directApiAccess(<%= article.id %>);
        if (article) {
            alert('Article content accessed via API! Check console for details.');
            
            // Display the content
            const contentDiv = document.createElement('div');
            contentDiv.className = 'alert alert-success mt-3';
            contentDiv.innerHTML = `
                <h6>API Bypass Successful!</h6>
                <p><strong>Full Content:</strong></p>
                <p>${article.content}</p>
            `;
            document.querySelector('.col-md-8 .card-body').appendChild(contentDiv);
        }
    }
    
    // Educational: Log current protection status
    console.log('Paywall Protection Status:', {
        articleId: <%= article.id %>,
        paywallType: '<%= paywallType %>',
        clientSideBypass: new URLSearchParams(window.location.search).get('bypass') === 'client',
        cookieBypass: document.cookie.includes('premium_user=true'),
        currentCookies: document.cookie,
        currentHeaders: 'Check Network tab in DevTools'
    });
    
    // Show client-side paywall after 3 seconds (educational demonstration)
    setTimeout(() => {
        if (Math.random() > 0.5) { // 50% chance to show overlay
            showClientSidePaywall();
        }
    }, 3000);
</script>
