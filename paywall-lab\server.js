const express = require('express');
const path = require('path');
const cookieParser = require('cookie-parser');
const session = require('express-session');
const expressLayouts = require('express-ejs-layouts');

const app = express();
const PORT = 3000;

// Middleware
app.use(express.static('public'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(session({
    secret: 'educational-lab-secret',
    resave: false,
    saveUninitialized: true,
    cookie: { maxAge: 24 * 60 * 60 * 1000 } // 24 hours
}));

// Set view engine
app.use(expressLayouts);
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
app.set('layout', 'layout');

// Mock user database
const users = {
    '<EMAIL>': { password: 'password123', subscription: false },
    '<EMAIL>': { password: 'premium123', subscription: true }
};

// Mock articles database
const articles = [
    {
        id: 1,
        title: "Breaking: Major Cybersecurity Breach Discovered",
        summary: "A significant security vulnerability has been found in popular web applications...",
        content: "This is the full article content that should be behind a paywall. In a major cybersecurity development, researchers have discovered a critical vulnerability that affects millions of web applications worldwide. The vulnerability, dubbed 'PaywallBypass-2024', allows attackers to circumvent payment verification systems through various injection techniques. Security experts recommend immediate patching and implementation of server-side validation mechanisms.",
        premium: true,
        views: 0
    },
    {
        id: 2,
        title: "Free Article: Introduction to Web Security",
        summary: "Learn the basics of web application security in this introductory article...",
        content: "This is a free article available to all users. Web security is a critical aspect of modern application development. Understanding common vulnerabilities and attack vectors is essential for developers and security professionals.",
        premium: false,
        views: 0
    },
    {
        id: 3,
        title: "Advanced: Server-Side Security Patterns",
        summary: "Deep dive into server-side security implementation patterns...",
        content: "This premium content covers advanced server-side security patterns including proper authentication, authorization, input validation, and secure session management. These techniques are essential for building robust web applications that can withstand modern attack vectors.",
        premium: true,
        views: 0
    }
];

// Routes
app.get('/', (req, res) => {
    res.render('index', { 
        articles: articles.map(a => ({ ...a, content: a.summary })),
        user: req.session.user 
    });
});

app.get('/article/:id', (req, res) => {
    const articleId = parseInt(req.params.id);
    const article = articles.find(a => a.id === articleId);
    
    if (!article) {
        return res.status(404).render('error', { message: 'Article not found' });
    }

    // Increment view count (vulnerable to manipulation)
    article.views++;

    // Check if article is premium
    if (article.premium) {
        // Multiple paywall protection layers for educational purposes
        
        // Layer 1: Client-side check (easily bypassable)
        const clientSideBypass = req.query.bypass === 'client';
        
        // Layer 2: Cookie-based subscription check (vulnerable to cookie manipulation)
        const cookieBypass = req.cookies.premium_user === 'true';
        
        // Layer 3: Session-based check (more secure but still vulnerable)
        const sessionBypass = req.session.user && req.session.user.subscription;
        
        // Layer 4: Header-based check (vulnerable to header injection)
        const headerBypass = req.headers['x-premium-access'] === 'granted';
        
        // Layer 5: Referrer-based check (vulnerable to referrer spoofing)
        const referrerBypass = req.headers.referer && req.headers.referer.includes('premium');

        if (!clientSideBypass && !cookieBypass && !sessionBypass && !headerBypass && !referrerBypass) {
            return res.render('paywall', { 
                article: { ...article, content: article.summary },
                paywallType: 'hybrid'
            });
        }
    }

    res.render('article', { article, user: req.session.user });
});

app.post('/login', (req, res) => {
    const { email, password } = req.body;
    const user = users[email];
    
    if (user && user.password === password) {
        req.session.user = { email, subscription: user.subscription };
        res.json({ success: true, subscription: user.subscription });
    } else {
        res.json({ success: false, message: 'Invalid credentials' });
    }
});

app.post('/logout', (req, res) => {
    req.session.destroy();
    res.json({ success: true });
});

// API endpoint vulnerable to direct access
app.get('/api/article/:id', (req, res) => {
    const articleId = parseInt(req.params.id);
    const article = articles.find(a => a.id === articleId);
    
    if (!article) {
        return res.status(404).json({ error: 'Article not found' });
    }

    // Vulnerable: No authentication check on API endpoint
    res.json(article);
});

// Admin endpoint (intentionally vulnerable for educational purposes)
app.get('/admin/articles', (req, res) => {
    // Vulnerable: No proper admin authentication
    if (req.headers['x-admin-token'] === 'admin123') {
        res.json(articles);
    } else {
        res.status(403).json({ error: 'Access denied' });
    }
});

app.listen(PORT, () => {
    console.log(`Educational Paywall Lab running on http://localhost:${PORT}`);
    console.log('This is for educational purposes only!');
});
