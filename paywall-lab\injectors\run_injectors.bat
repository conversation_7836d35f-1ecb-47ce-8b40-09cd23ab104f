@echo off
echo ========================================
echo Educational Paywall Bypass Injectors
echo For Cybersecurity Education Only
echo ========================================
echo.

:menu
echo Select an injector to run:
echo 1. Advanced Bypass Tool (Comprehensive)
echo 2. Universal Paywall Bypass
echo 3. <PERSON>ie Injector (Legacy)
echo 4. Header Injector (Legacy)
echo 5. Browser Injector (Legacy)
echo 6. JWT-Only Advanced Bypass
echo 7. Rate Limit Bypass Test
echo 8. Run All Advanced Tools
echo 9. Install Dependencies
echo 0. Exit
echo.

set /p choice="Enter your choice (0-9): "

if "%choice%"=="1" goto advanced_comprehensive
if "%choice%"=="2" goto universal_bypass
if "%choice%"=="3" goto cookie_legacy
if "%choice%"=="4" goto header_legacy
if "%choice%"=="5" goto browser_legacy
if "%choice%"=="6" goto jwt_only
if "%choice%"=="7" goto rate_limit_test
if "%choice%"=="8" goto all_advanced
if "%choice%"=="9" goto install_deps
if "%choice%"=="0" goto exit
goto menu

:advanced_comprehensive
echo Running Advanced Bypass Tool (Comprehensive)...
python advanced_bypass.py --comprehensive
pause
goto menu

:universal_bypass
echo Running Universal Paywall Bypass...
python universal_paywall_bypass.py
pause
goto menu

:cookie_legacy
echo Running Cookie Injector (Legacy)...
python cookie_injector.py
pause
goto menu

:header_legacy
echo Running Header Injector (Legacy)...
python header_injector.py
pause
goto menu

:browser_legacy
echo Running Browser Injector (Legacy)...
python browser_injector.py
pause
goto menu

:jwt_only
echo Running JWT-Only Advanced Bypass...
python advanced_bypass.py --jwt-only
pause
goto menu

:rate_limit_test
echo Running Rate Limit Bypass Test...
python advanced_bypass.py --rate-limit-only
pause
goto menu

:all_advanced
echo Running All Advanced Tools...
echo.
echo === Advanced Bypass Tool ===
python advanced_bypass.py --comprehensive
echo.
echo === Universal Paywall Bypass ===
python universal_paywall_bypass.py
echo.
echo === Legacy Tools Test ===
python cookie_injector.py
python header_injector.py
pause
goto menu

:install_deps
echo Installing Python dependencies...
pip install -r requirements.txt
echo.
echo Dependencies installed!
pause
goto menu

:exit
echo Goodbye!
exit /b 0
