@echo off
echo ========================================
echo Educational Paywall Bypass Injectors
echo For Cybersecurity Education Only
echo ========================================
echo.

:menu
echo Select an injector to run:
echo 1. <PERSON><PERSON> Injector (Basic)
echo 2. <PERSON>ie Injector (Full Demo)
echo 3. Header Injector (Basic)
echo 4. Header Injector (Full Demo)
echo 5. Browser Injector (Basic)
echo 6. Browser Injector (Full Demo)
echo 7. Run All Injectors (Full Demo)
echo 8. Install Dependencies
echo 9. Exit
echo.

set /p choice="Enter your choice (1-9): "

if "%choice%"=="1" goto cookie_basic
if "%choice%"=="2" goto cookie_demo
if "%choice%"=="3" goto header_basic
if "%choice%"=="4" goto header_demo
if "%choice%"=="5" goto browser_basic
if "%choice%"=="6" goto browser_demo
if "%choice%"=="7" goto all_demo
if "%choice%"=="8" goto install_deps
if "%choice%"=="9" goto exit
goto menu

:cookie_basic
echo Running Cookie Injector (Basic)...
python cookie_injector.py
pause
goto menu

:cookie_demo
echo Running Cookie Injector (Full Demo)...
python cookie_injector.py --demo
pause
goto menu

:header_basic
echo Running Header Injector (Basic)...
python header_injector.py
pause
goto menu

:header_demo
echo Running Header Injector (Full Demo)...
python header_injector.py --demo
pause
goto menu

:browser_basic
echo Running Browser Injector (Basic)...
python browser_injector.py
pause
goto menu

:browser_demo
echo Running Browser Injector (Full Demo)...
python browser_injector.py --demo
pause
goto menu

:all_demo
echo Running All Injectors (Full Demo)...
echo.
echo === Cookie Injector Demo ===
python cookie_injector.py --demo
echo.
echo === Header Injector Demo ===
python header_injector.py --demo
echo.
echo === Browser Injector Demo ===
python browser_injector.py --demo
pause
goto menu

:install_deps
echo Installing Python dependencies...
pip install -r requirements.txt
echo.
echo Dependencies installed!
pause
goto menu

:exit
echo Goodbye!
exit /b 0
