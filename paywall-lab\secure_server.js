const express = require('express');
const path = require('path');
const cookieParser = require('cookie-parser');
const session = require('express-session');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const cors = require('cors');

const app = express();
const PORT = 3000;
const JWT_SECRET = 'educational-lab-secret-key-change-in-production';

// Security middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
            scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
}));
app.use(cors());

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100,
    message: 'Too many requests from this IP, please try again later.'
});
app.use(limiter);

const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 5,
    message: 'Too many authentication attempts, please try again later.'
});

// Middleware
app.use(express.static('public'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(session({
    secret: JWT_SECRET,
    resave: false,
    saveUninitialized: false,
    cookie: { 
        maxAge: 24 * 60 * 60 * 1000,
        httpOnly: true,
        secure: false,
        sameSite: 'strict'
    }
}));

app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Secure user database with hashed passwords
const users = {
    '<EMAIL>': { 
        password: bcrypt.hashSync('password123', 10), 
        subscription: false,
        id: 1
    },
    '<EMAIL>': { 
        password: bcrypt.hashSync('premium123', 10), 
        subscription: true,
        id: 2
    }
};

// Articles database with enhanced security metadata
const articles = [
    {
        id: 1,
        title: "Breaking: Major Cybersecurity Breach Discovered",
        summary: "A significant security vulnerability has been found in popular web applications...",
        content: "This is the full article content that should be behind a paywall. In a major cybersecurity development, researchers have discovered a critical vulnerability that affects millions of web applications worldwide. The vulnerability, dubbed 'PaywallBypass-2024', allows attackers to circumvent payment verification systems through various injection techniques. Security experts recommend immediate patching and implementation of server-side validation mechanisms. This comprehensive analysis covers the technical details, impact assessment, and recommended mitigation strategies for organizations worldwide.",
        premium: true,
        views: 0,
        securityLevel: 'high',
        requiresValidToken: true
    },
    {
        id: 2,
        title: "Free Article: Introduction to Web Security",
        summary: "Learn the basics of web application security in this introductory article...",
        content: "This is a free article available to all users. Web security is a critical aspect of modern application development. Understanding common vulnerabilities and attack vectors is essential for developers and security professionals.",
        premium: false,
        views: 0,
        securityLevel: 'none',
        requiresValidToken: false
    },
    {
        id: 3,
        title: "Advanced: Server-Side Security Patterns",
        summary: "Deep dive into server-side security implementation patterns...",
        content: "This premium content covers advanced server-side security patterns including proper authentication, authorization, input validation, and secure session management. These techniques are essential for building robust web applications that can withstand modern attack vectors.",
        premium: true,
        views: 0,
        securityLevel: 'maximum',
        requiresValidToken: true
    }
];

// JWT token verification middleware
function verifyToken(req, res, next) {
    const token = req.headers.authorization?.split(' ')[1] || req.cookies.authToken;
    
    if (!token) {
        return res.status(401).json({ error: 'Access denied. No token provided.' });
    }
    
    try {
        const decoded = jwt.verify(token, JWT_SECRET);
        req.user = decoded;
        next();
    } catch (error) {
        return res.status(403).json({ error: 'Invalid token.' });
    }
}

// Premium content access middleware
function requirePremium(req, res, next) {
    if (!req.user || !req.user.subscription) {
        return res.status(403).json({ error: 'Premium subscription required.' });
    }
    next();
}

// Server-side paywall validation
function validateAccess(article, user) {
    // Free articles are always accessible
    if (!article.premium) {
        return { allowed: true, reason: 'free_content' };
    }
    
    // Check if user exists and has valid subscription
    if (!user || !user.subscription) {
        return { allowed: false, reason: 'no_subscription' };
    }
    
    // Additional security checks for high-security content
    if (article.securityLevel === 'maximum') {
        // Require recent authentication (within last hour)
        const tokenAge = Date.now() - (user.iat * 1000);
        if (tokenAge > 3600000) { // 1 hour
            return { allowed: false, reason: 'token_expired' };
        }
    }
    
    return { allowed: true, reason: 'valid_subscription' };
}

// Routes
app.get('/', (req, res) => {
    const user = req.session.user;
    res.render('index', { 
        articles: articles.map(a => ({ ...a, content: a.summary })),
        user: user 
    });
});

app.get('/article/:id', (req, res) => {
    const articleId = parseInt(req.params.id);
    const article = articles.find(a => a.id === articleId);
    
    if (!article) {
        return res.status(404).render('error', { message: 'Article not found' });
    }
    
    // Increment view count
    article.views++;
    
    // Server-side access validation
    const accessCheck = validateAccess(article, req.session.user);
    
    if (!accessCheck.allowed) {
        return res.render('paywall_secure', {
            article: { ...article, content: article.summary },
            reason: accessCheck.reason,
            securityLevel: article.securityLevel
        });
    }
    
    res.render('article', { article, user: req.session.user });
});

// Secure authentication endpoint
app.post('/login', authLimiter, async (req, res) => {
    const { email, password } = req.body;
    
    if (!email || !password) {
        return res.status(400).json({ success: false, message: 'Email and password required' });
    }
    
    const user = users[email];
    
    if (!user || !bcrypt.compareSync(password, user.password)) {
        return res.status(401).json({ success: false, message: 'Invalid credentials' });
    }
    
    // Create JWT token
    const token = jwt.sign(
        { 
            id: user.id,
            email: email,
            subscription: user.subscription,
            iat: Math.floor(Date.now() / 1000)
        },
        JWT_SECRET,
        { expiresIn: '24h' }
    );
    
    // Set secure session
    req.session.user = { 
        id: user.id,
        email: email, 
        subscription: user.subscription 
    };
    
    // Set secure HTTP-only cookie
    res.cookie('authToken', token, {
        httpOnly: true,
        secure: false, // Set to true in production
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000
    });
    
    res.json({ success: true, subscription: user.subscription });
});

app.post('/logout', (req, res) => {
    req.session.destroy();
    res.clearCookie('authToken');
    res.json({ success: true });
});

// Secure API endpoint with proper authentication
app.get('/api/article/:id', verifyToken, (req, res) => {
    const articleId = parseInt(req.params.id);
    const article = articles.find(a => a.id === articleId);
    
    if (!article) {
        return res.status(404).json({ error: 'Article not found' });
    }
    
    // Server-side access validation for API
    const accessCheck = validateAccess(article, req.user);
    
    if (!accessCheck.allowed) {
        return res.status(403).json({ 
            error: 'Access denied',
            reason: accessCheck.reason,
            premium: article.premium
        });
    }
    
    res.json(article);
});

// Secure admin endpoint
app.get('/admin/articles', verifyToken, (req, res) => {
    // In a real application, you'd check for admin role
    if (req.user.email !== '<EMAIL>') {
        return res.status(403).json({ error: 'Admin access required' });
    }
    
    res.json(articles);
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
    console.log(`Secure Educational Paywall Lab running on http://localhost:${PORT}`);
    console.log('This is for educational purposes only!');
    console.log('Security features enabled: JWT tokens, rate limiting, secure sessions');
});
