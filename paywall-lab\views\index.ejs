<% layout('layout') -%>

<div class="row">
    <div class="col-md-8">
        <h1>Latest Security News</h1>
        <p class="text-muted">Educational articles for cybersecurity learning</p>
        
        <% articles.forEach(article => { %>
            <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h5 class="card-title">
                            <a href="/article/<%= article.id %>" class="text-decoration-none">
                                <%= article.title %>
                            </a>
                        </h5>
                        <% if (article.premium) { %>
                            <span class="premium-badge">PREMIUM</span>
                        <% } %>
                    </div>
                    <p class="card-text"><%= article.content %></p>
                    <small class="text-muted">Views: <%= article.views %></small>
                </div>
            </div>
        <% }); %>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>Lab Information</h5>
            </div>
            <div class="card-body">
                <h6>Available Accounts:</h6>
                <ul class="list-unstyled">
                    <li><strong>Free User:</strong><br>
                        Email: <EMAIL><br>
                        Password: password123
                    </li>
                    <li class="mt-2"><strong>Premium User:</strong><br>
                        Email: <EMAIL><br>
                        Password: premium123
                    </li>
                </ul>
                
                <hr>
                
                <h6>Vulnerability Types:</h6>
                <ul class="small">
                    <li>Client-side validation bypass</li>
                    <li>Cookie manipulation</li>
                    <li>Header injection</li>
                    <li>Direct API access</li>
                    <li>Session manipulation</li>
                    <li>Referrer spoofing</li>
                </ul>
                
                <hr>
                
                <h6>Browser Console Commands:</h6>
                <div class="small">
                    <code>bypassPaywall.setCookie('premium_user', 'true')</code><br>
                    <code>bypassPaywall.directApiAccess(1)</code>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5>Educational Objectives</h5>
            </div>
            <div class="card-body small">
                <ul>
                    <li>Understand different paywall implementation methods</li>
                    <li>Learn common bypass techniques</li>
                    <li>Practice ethical security testing</li>
                    <li>Develop secure coding practices</li>
                </ul>
            </div>
        </div>
    </div>
</div>
