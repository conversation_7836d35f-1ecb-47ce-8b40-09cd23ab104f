@echo off
echo ========================================
echo Starting Educational Paywall Lab
echo ========================================
echo.

:: Check if the web application is already running
netstat -an | find "3000" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo WARNING: Port 3000 is already in use
    echo The web application might already be running
    echo.
    choice /c YN /m "Do you want to continue anyway"
    if errorlevel 2 exit /b 1
)

echo Starting the web application...
echo.
echo The application will be available at: http://localhost:3000
echo.
echo Press Ctrl+C to stop the server
echo.

:: Start the Node.js application
npm start
