@echo off
echo ========================================
echo Educational Paywall Security Lab Setup
echo ========================================
echo.

echo Checking prerequisites...

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please download and install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)
echo ✓ Node.js is installed

:: Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please download and install Python from: https://www.python.org/
    pause
    exit /b 1
)
echo ✓ Python is installed

:: Check if pip is available
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: pip is not available
    echo Please ensure pip is installed with Python
    pause
    exit /b 1
)
echo ✓ pip is available

echo.
echo Installing dependencies...

:: Install Node.js dependencies
echo Installing Node.js dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install Node.js dependencies
    pause
    exit /b 1
)
echo ✓ Node.js dependencies installed

:: Install Python dependencies
echo Installing Python dependencies...
cd injectors
call pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ERROR: Failed to install Python dependencies
    pause
    exit /b 1
)
cd ..
echo ✓ Python dependencies installed

echo.
echo ========================================
echo Lab Setup Complete!
echo ========================================
echo.
echo To start the lab:
echo 1. Run: npm start (to start the web application)
echo 2. Open: http://localhost:3000 in your browser
echo 3. Navigate to injectors folder and run: run_injectors.bat
echo.
echo Test accounts:
echo - Free user: <EMAIL> / password123
echo - Premium user: <EMAIL> / premium123
echo.
pause
