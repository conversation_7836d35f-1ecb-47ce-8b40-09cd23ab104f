# Educational Paywall Security Lab

## ⚠️ IMPORTANT DISCLAIMER
This lab is designed for **educational purposes only** in a controlled environment. The techniques demonstrated here should **never** be used against real websites without explicit permission. This lab is intended to teach cybersecurity concepts and help students understand web application vulnerabilities.

## 🎓 Learning Objectives

By completing this lab, students will:
- Understand different paywall implementation methods
- Learn common bypass techniques and their limitations
- Practice ethical security testing in a controlled environment
- Develop secure coding practices to prevent these vulnerabilities
- Gain hands-on experience with web security tools

## 📋 Lab Overview

This lab consists of:
1. **Mock News Website** - A realistic web application with intentional vulnerabilities
2. **Educational Bypass Tools** - Python scripts demonstrating various attack techniques
3. **Comprehensive Documentation** - Detailed explanations and exercises

## 🛠️ Setup Instructions

### Prerequisites
- Windows 10/11
- Python 3.8 or higher
- Node.js 16 or higher
- Chrome browser (for browser automation)
- ChromeDriver (for Selenium)

### Installation Steps

1. **Clone or download this lab to your computer**

2. **Install Node.js dependencies for the web application:**
   ```bash
   cd paywall-lab
   npm install
   ```

3. **Install Python dependencies for the injectors:**
   ```bash
   cd injectors
   pip install -r requirements.txt
   ```

4. **Install ChromeDriver for browser automation:**
   - Download from: https://chromedriver.chromium.org/
   - Add to your system PATH

## 🚀 Running the Lab

### Step 1: Start the Vulnerable Web Application
```bash
cd paywall-lab
npm start
```
The application will be available at: http://localhost:3000

### Step 2: Explore the Website
- Visit the homepage and browse articles
- Try accessing premium articles (they should be blocked)
- Test the login functionality with provided credentials

### Step 3: Run the Bypass Tools
Navigate to the `injectors` directory and run:

**Windows Batch Script:**
```bash
run_injectors.bat
```

**PowerShell Script:**
```powershell
.\run_injectors.ps1
```

**Individual Tools:**
```bash
python cookie_injector.py --demo
python header_injector.py --demo
python browser_injector.py --demo
```

## 🔍 Vulnerability Types Demonstrated

### 1. Client-Side Validation Bypass
- **Vulnerability:** Paywall checks performed only in browser JavaScript
- **Bypass Method:** URL parameter manipulation, DOM modification
- **Example:** `http://localhost:3000/article/1?bypass=client`

### 2. Cookie Manipulation
- **Vulnerability:** Premium status stored in unencrypted cookies
- **Bypass Method:** Cookie injection and modification
- **Example:** Setting `premium_user=true` cookie

### 3. HTTP Header Injection
- **Vulnerability:** Server trusts client-provided headers
- **Bypass Method:** Adding custom headers like `X-Premium-Access: granted`
- **Example:** Using proxy tools or custom requests

### 4. Direct API Access
- **Vulnerability:** API endpoints lack proper authentication
- **Bypass Method:** Direct requests to `/api/article/{id}`
- **Example:** Bypassing frontend restrictions entirely

### 5. Session Manipulation
- **Vulnerability:** Weak session management
- **Bypass Method:** Session cookie modification
- **Example:** Injecting fake session data

### 6. Referrer Spoofing
- **Vulnerability:** Access control based on HTTP referrer
- **Bypass Method:** Modifying referrer header
- **Example:** Setting referrer to contain "premium"

## 🧪 Lab Exercises

### Exercise 1: Manual Bypass Techniques
1. Open the website in your browser
2. Navigate to a premium article
3. Use browser developer tools to:
   - Modify cookies
   - Edit DOM elements
   - Remove paywall overlays
   - Inspect network requests

### Exercise 2: Automated Bypass Tools
1. Run each injector tool individually
2. Observe the different techniques used
3. Compare success rates of different methods
4. Analyze the network traffic generated

### Exercise 3: Security Analysis
1. Identify which bypass methods work
2. Understand why each method succeeds or fails
3. Propose security improvements
4. Implement and test fixes

### Exercise 4: Ethical Considerations
1. Discuss the legal and ethical implications
2. Understand responsible disclosure
3. Learn about bug bounty programs
4. Practice professional security testing

## 📊 Expected Results

### Successful Bypass Indicators
- ✅ Access to full premium article content
- ✅ Removal of paywall overlays
- ✅ Direct API data retrieval
- ✅ Console messages indicating bypass success

### Failed Bypass Indicators
- ❌ Paywall still visible
- ❌ Limited content access
- ❌ HTTP 403/401 errors
- ❌ Server-side validation blocking access

## 🔧 Troubleshooting

### Common Issues

**Web Application Won't Start:**
- Check if Node.js is installed: `node --version`
- Verify port 3000 is available
- Run `npm install` to install dependencies

**Python Scripts Fail:**
- Check Python version: `python --version`
- Install dependencies: `pip install -r requirements.txt`
- Verify the web application is running

**Browser Automation Fails:**
- Install ChromeDriver and add to PATH
- Check Chrome browser version compatibility
- Try running with `--headless` flag

**No Bypass Success:**
- Verify the web application is running on localhost:3000
- Check network connectivity
- Review console output for error messages

## 📚 Additional Resources

### Web Security Learning
- OWASP Top 10: https://owasp.org/www-project-top-ten/
- Web Security Academy: https://portswigger.net/web-security
- SANS Web Application Security: https://www.sans.org/

### Tools and Frameworks
- Burp Suite: https://portswigger.net/burp
- OWASP ZAP: https://www.zaproxy.org/
- Selenium Documentation: https://selenium-python.readthedocs.io/

### Ethical Hacking
- Bug Bounty Platforms: HackerOne, Bugcrowd
- Responsible Disclosure Guidelines
- Legal Frameworks for Security Testing

## 🎯 Assessment Criteria

Students will be evaluated on:
1. **Technical Understanding** - Comprehension of vulnerability types
2. **Practical Skills** - Ability to execute bypass techniques
3. **Analysis Capability** - Understanding of why techniques work/fail
4. **Security Mindset** - Proposing effective countermeasures
5. **Ethical Awareness** - Understanding legal and ethical boundaries

## 📝 Lab Report Template

Students should submit a report including:
1. **Executive Summary** - Overview of findings
2. **Methodology** - Tools and techniques used
3. **Results** - Success/failure of each bypass method
4. **Analysis** - Technical explanation of vulnerabilities
5. **Recommendations** - Security improvements
6. **Reflection** - Ethical considerations and learning outcomes

## 🤝 Support

For technical support or questions about this lab:
- Review the troubleshooting section
- Check console output for error messages
- Consult the additional resources
- Ask your instructor for assistance

---

**Remember: This lab is for educational purposes only. Always practice ethical hacking and respect legal boundaries.**
