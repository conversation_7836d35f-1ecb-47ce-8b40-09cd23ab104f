<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SecureNews - Advanced Security Lab</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .paywall-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .paywall-content {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            max-width: 500px;
            text-align: center;
        }
        
        .article-preview {
            max-height: 200px;
            overflow: hidden;
            position: relative;
        }
        
        .article-preview::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: linear-gradient(transparent, white);
        }
        
        .premium-badge {
            background: #ffd700;
            color: #333;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .lab-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }

        .security-indicator {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 8px;
        }

        .security-high { background: #dc3545; color: white; }
        .security-maximum { background: #6f42c1; color: white; }
        .security-none { background: #28a745; color: white; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">🔒 SecureNews Lab</a>
            <div class="navbar-nav ms-auto">
                <% if (typeof user !== 'undefined' && user) { %>
                    <span class="navbar-text me-3">
                        Welcome, <%= user.email %> 
                        <% if (user.subscription) { %>
                            <span class="premium-badge">PREMIUM</span>
                        <% } %>
                    </span>
                    <button class="btn btn-outline-light btn-sm" onclick="logout()">Logout</button>
                <% } else { %>
                    <button class="btn btn-outline-light btn-sm" data-bs-toggle="modal" data-bs-target="#loginModal">Login</button>
                <% } %>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="lab-warning">
            <strong>🔒 Advanced Security Lab Environment</strong><br>
            This system implements multiple security layers including JWT tokens, rate limiting,
            and server-side validation. Test your bypass skills against real security measures.
        </div>
        
        <%- body %>
    </div>

    <!-- Login Modal -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Login</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" required>
                            <div class="form-text">
                                Try: <EMAIL> (free) or <EMAIL> (premium)
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" required>
                            <div class="form-text">
                                Password: password123 or premium123
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Login</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Login functionality
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            const response = await fetch('/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email, password })
            });
            
            const result = await response.json();
            if (result.success) {
                location.reload();
            } else {
                alert('Login failed: ' + result.message);
            }
        });
        
        // Logout functionality
        async function logout() {
            await fetch('/logout', { method: 'POST' });
            location.reload();
        }
        
        // Security testing functions (educational purposes)
        window.securityTest = {
            testApiAccess: async (articleId) => {
                try {
                    const response = await fetch(`/api/article/${articleId}`, {
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('authToken') || 'invalid'}`
                        }
                    });
                    const result = await response.json();
                    console.log('API Test Result:', result);
                    return result;
                } catch (error) {
                    console.error('API test failed:', error);
                    return { error: error.message };
                }
            },

            checkTokenStatus: () => {
                const token = localStorage.getItem('authToken');
                if (token) {
                    try {
                        const payload = JSON.parse(atob(token.split('.')[1]));
                        console.log('Token payload:', payload);
                        console.log('Token expires:', new Date(payload.exp * 1000));
                        return payload;
                    } catch (e) {
                        console.log('Invalid token format');
                        return null;
                    }
                } else {
                    console.log('No token found');
                    return null;
                }
            },

            testRateLimit: async () => {
                console.log('Testing rate limiting...');
                for (let i = 0; i < 10; i++) {
                    try {
                        const response = await fetch('/health');
                        console.log(`Request ${i + 1}: ${response.status}`);
                    } catch (error) {
                        console.log(`Request ${i + 1} failed:`, error.message);
                    }
                }
            }
        };
    </script>
</body>
</html>
