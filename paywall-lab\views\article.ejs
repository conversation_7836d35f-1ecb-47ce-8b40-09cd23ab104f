<% layout('layout') -%>

<div class="row">
    <div class="col-md-8">
        <article>
            <div class="d-flex justify-content-between align-items-start mb-3">
                <h1><%= article.title %></h1>
                <% if (article.premium) { %>
                    <span class="premium-badge">PREMIUM</span>
                <% } %>
            </div>
            
            <div class="text-muted mb-4">
                Views: <%= article.views %> | 
                Article ID: <%= article.id %> |
                <% if (article.premium) { %>
                    <span class="text-success">✓ Access Granted</span>
                <% } else { %>
                    <span class="text-info">Free Article</span>
                <% } %>
            </div>
            
            <div class="article-content">
                <%= article.content %>
            </div>
            
            <hr class="my-4">
            
            <div class="alert alert-info">
                <h6>🎓 Educational Note:</h6>
                <p class="mb-0">
                    You successfully accessed this content! If this was a premium article, 
                    consider how you bypassed the paywall and what security measures could prevent this.
                </p>
            </div>
        </article>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>Article Analysis</h5>
            </div>
            <div class="card-body">
                <h6>Technical Details:</h6>
                <ul class="small">
                    <li><strong>Article ID:</strong> <%= article.id %></li>
                    <li><strong>Premium Status:</strong> <%= article.premium ? 'Yes' : 'No' %></li>
                    <li><strong>Current Views:</strong> <%= article.views %></li>
                </ul>
                
                <% if (article.premium) { %>
                    <hr>
                    <h6>Bypass Methods Used:</h6>
                    <div class="small">
                        <p>Check the browser's developer tools to see:</p>
                        <ul>
                            <li>Network requests</li>
                            <li>Cookies set</li>
                            <li>Headers sent</li>
                            <li>JavaScript console output</li>
                        </ul>
                    </div>
                <% } %>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5>Security Learning</h5>
            </div>
            <div class="card-body small">
                <h6>Questions to Consider:</h6>
                <ul>
                    <li>How could this paywall be made more secure?</li>
                    <li>What server-side validations are missing?</li>
                    <li>How can client-side restrictions be bypassed?</li>
                    <li>What role does authentication play?</li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5>Try These Techniques</h5>
            </div>
            <div class="card-body small">
                <p>Open browser console and try:</p>
                <code>bypassPaywall.setCookie('premium_user', 'true')</code><br><br>
                <code>bypassPaywall.directApiAccess(<%= article.id %>)</code><br><br>
                <p>Or modify the URL with: <code>?bypass=client</code></p>
            </div>
        </div>
    </div>
</div>

<script>
    // Educational: Show how content was accessed
    console.log('Article accessed:', {
        id: <%= article.id %>,
        title: '<%= article.title %>',
        premium: <%= article.premium %>,
        accessMethod: 'Direct page load'
    });
    
    // Check for bypass indicators
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('bypass')) {
        console.log('Client-side bypass detected:', urlParams.get('bypass'));
    }
    
    if (document.cookie.includes('premium_user=true')) {
        console.log('Cookie-based bypass detected');
    }
</script>
