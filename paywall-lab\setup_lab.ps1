# Educational Paywall Security Lab Setup Script
# PowerShell version for Windows

param(
    [switch]$SkipChecks = $false,
    [switch]$Verbose = $false
)

function Write-Status {
    param([string]$Message, [string]$Type = "Info")
    
    switch ($Type) {
        "Success" { Write-Host "✓ $Message" -ForegroundColor Green }
        "Error" { Write-Host "✗ $Message" -ForegroundColor Red }
        "Warning" { Write-Host "⚠ $Message" -ForegroundColor Yellow }
        "Info" { Write-Host "ℹ $Message" -ForegroundColor Cyan }
        default { Write-Host $Message }
    }
}

function Test-Command {
    param([string]$Command)
    
    try {
        $null = Get-Command $Command -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

function Test-Prerequisites {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Educational Paywall Security Lab Setup" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Status "Checking prerequisites..." "Info"
    
    $allGood = $true
    
    # Check Node.js
    if (Test-Command "node") {
        $nodeVersion = node --version
        Write-Status "Node.js is installed ($nodeVersion)" "Success"
    } else {
        Write-Status "Node.js is not installed or not in PATH" "Error"
        Write-Status "Please download and install Node.js from: https://nodejs.org/" "Warning"
        $allGood = $false
    }
    
    # Check npm
    if (Test-Command "npm") {
        $npmVersion = npm --version
        Write-Status "npm is available ($npmVersion)" "Success"
    } else {
        Write-Status "npm is not available" "Error"
        $allGood = $false
    }
    
    # Check Python
    if (Test-Command "python") {
        $pythonVersion = python --version
        Write-Status "Python is installed ($pythonVersion)" "Success"
    } else {
        Write-Status "Python is not installed or not in PATH" "Error"
        Write-Status "Please download and install Python from: https://www.python.org/" "Warning"
        $allGood = $false
    }
    
    # Check pip
    if (Test-Command "pip") {
        $pipVersion = pip --version
        Write-Status "pip is available" "Success"
        if ($Verbose) { Write-Host "  $pipVersion" -ForegroundColor Gray }
    } else {
        Write-Status "pip is not available" "Error"
        Write-Status "Please ensure pip is installed with Python" "Warning"
        $allGood = $false
    }
    
    # Check Chrome (optional for browser automation)
    $chromeInstalled = $false
    $chromePaths = @(
        "${env:ProgramFiles}\Google\Chrome\Application\chrome.exe",
        "${env:ProgramFiles(x86)}\Google\Chrome\Application\chrome.exe",
        "${env:LOCALAPPDATA}\Google\Chrome\Application\chrome.exe"
    )
    
    foreach ($path in $chromePaths) {
        if (Test-Path $path) {
            Write-Status "Chrome browser found" "Success"
            $chromeInstalled = $true
            break
        }
    }
    
    if (-not $chromeInstalled) {
        Write-Status "Chrome browser not found (optional for browser automation)" "Warning"
        Write-Status "Download from: https://www.google.com/chrome/" "Info"
    }
    
    return $allGood
}

function Install-Dependencies {
    Write-Host ""
    Write-Status "Installing dependencies..." "Info"
    
    # Install Node.js dependencies
    Write-Status "Installing Node.js dependencies..." "Info"
    try {
        $npmOutput = npm install 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Status "Node.js dependencies installed successfully" "Success"
            if ($Verbose) { Write-Host $npmOutput -ForegroundColor Gray }
        } else {
            Write-Status "Failed to install Node.js dependencies" "Error"
            Write-Host $npmOutput -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Status "Error running npm install: $_" "Error"
        return $false
    }
    
    # Install Python dependencies
    Write-Status "Installing Python dependencies..." "Info"
    try {
        Push-Location "injectors"
        $pipOutput = pip install -r requirements.txt 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Status "Python dependencies installed successfully" "Success"
            if ($Verbose) { Write-Host $pipOutput -ForegroundColor Gray }
        } else {
            Write-Status "Failed to install Python dependencies" "Error"
            Write-Host $pipOutput -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Status "Error installing Python dependencies: $_" "Error"
        return $false
    }
    finally {
        Pop-Location
    }
    
    return $true
}

function Show-CompletionMessage {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "Lab Setup Complete!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "To start the lab:" -ForegroundColor Yellow
    Write-Host "1. Run: " -NoNewline; Write-Host "npm start" -ForegroundColor Cyan -NoNewline; Write-Host " (to start the web application)"
    Write-Host "2. Open: " -NoNewline; Write-Host "http://localhost:3000" -ForegroundColor Cyan -NoNewline; Write-Host " in your browser"
    Write-Host "3. Navigate to injectors folder and run: " -NoNewline; Write-Host ".\run_injectors.ps1" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "Test accounts:" -ForegroundColor Yellow
    Write-Host "- Free user: " -NoNewline; Write-Host "<EMAIL> / password123" -ForegroundColor Cyan
    Write-Host "- Premium user: " -NoNewline; Write-Host "<EMAIL> / premium123" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "Quick start commands:" -ForegroundColor Yellow
    Write-Host "- Start web app: " -NoNewline; Write-Host "npm start" -ForegroundColor Cyan
    Write-Host "- Run injectors: " -NoNewline; Write-Host "cd injectors && .\run_injectors.ps1" -ForegroundColor Cyan
    Write-Host "- Full demo: " -NoNewline; Write-Host "cd injectors && .\run_injectors.ps1 -Injector all" -ForegroundColor Cyan
}

function Test-Installation {
    Write-Host ""
    Write-Status "Testing installation..." "Info"
    
    # Test if package.json exists
    if (-not (Test-Path "package.json")) {
        Write-Status "package.json not found - are you in the correct directory?" "Error"
        return $false
    }
    
    # Test if injectors directory exists
    if (-not (Test-Path "injectors")) {
        Write-Status "injectors directory not found" "Error"
        return $false
    }
    
    # Test if Python scripts exist
    $pythonScripts = @("cookie_injector.py", "header_injector.py", "browser_injector.py")
    foreach ($script in $pythonScripts) {
        if (-not (Test-Path "injectors\$script")) {
            Write-Status "$script not found in injectors directory" "Error"
            return $false
        }
    }
    
    Write-Status "All required files found" "Success"
    return $true
}

# Main execution
try {
    if (-not $SkipChecks) {
        $prereqsOk = Test-Prerequisites
        if (-not $prereqsOk) {
            Write-Host ""
            Write-Status "Prerequisites check failed. Please install missing components and try again." "Error"
            exit 1
        }
    }
    
    if (-not (Test-Installation)) {
        Write-Status "Installation test failed" "Error"
        exit 1
    }
    
    $installOk = Install-Dependencies
    if (-not $installOk) {
        Write-Status "Dependency installation failed" "Error"
        exit 1
    }
    
    Show-CompletionMessage
}
catch {
    Write-Status "Setup failed with error: $_" "Error"
    exit 1
}
